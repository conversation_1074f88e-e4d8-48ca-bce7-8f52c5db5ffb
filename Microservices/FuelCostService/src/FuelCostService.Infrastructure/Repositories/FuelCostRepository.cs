using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FuelCostService.Core.Interfaces;
using FuelCostService.Core.Models;
using FuelCostService.Infrastructure.Data;

namespace FuelCostService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for FuelCosts operations
    /// </summary>
    public class FuelCostRepository : IFuelCostRepository
    {
        private readonly FuelCostDbContext _context;

        public FuelCostRepository(FuelCostDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<FuelCosts>> GetAllAsync()
        {
            return await _context.FuelCosts
                .Include(f => f.Electricity)
                .Include(f => f.NaturalGas)
                .Include(f => f.Oil)
                .Include(f => f.Propane)
                .Include(f => f.Wood)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .ToListAsync();
        }

        public async Task<FuelCosts?> GetByHouseIdAsync(Guid houseId)
        {
            return await _context.FuelCosts
                .Include(f => f.Electricity)
                .Include(f => f.NaturalGas)
                .Include(f => f.Oil)
                .Include(f => f.Propane)
                .Include(f => f.Wood)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .FirstOrDefaultAsync(f => f.HouseId == houseId);
        }

        public async Task<FuelCosts?> GetByIdAsync(Guid id)
        {
            return await _context.FuelCosts
                .Include(f => f.Electricity)
                .Include(f => f.NaturalGas)
                .Include(f => f.Oil)
                .Include(f => f.Propane)
                .Include(f => f.Wood)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        // Note: GetByEnergyUpgradeIdAsync method removed - energy upgrade functionality no longer needed

        public async Task<FuelCosts> CreateAsync(FuelCosts fuelCosts)
        {
            // Set FuelCostsId for all individual fuel cost entities
            if (fuelCosts.Electricity != null)
            {
                fuelCosts.Electricity.FuelCostsId = fuelCosts.Id;
            }
            if (fuelCosts.NaturalGas != null)
            {
                fuelCosts.NaturalGas.FuelCostsId = fuelCosts.Id;
            }
            if (fuelCosts.Oil != null)
            {
                fuelCosts.Oil.FuelCostsId = fuelCosts.Id;
            }
            if (fuelCosts.Propane != null)
            {
                fuelCosts.Propane.FuelCostsId = fuelCosts.Id;
            }
            if (fuelCosts.Wood != null)
            {
                fuelCosts.Wood.FuelCostsId = fuelCosts.Id;
            }

            // Add the main FuelCosts entity (EF will handle owned entities automatically)
            _context.FuelCosts.Add(fuelCosts);
            await _context.SaveChangesAsync();

            return fuelCosts;
        }

        public async Task UpdateAsync(FuelCosts fuelCosts)
        {
            // Set FuelCostsId for all individual fuel cost entities
            if (fuelCosts.Electricity != null)
            {
                fuelCosts.Electricity.FuelCostsId = fuelCosts.Id;
            }
            if (fuelCosts.NaturalGas != null)
            {
                fuelCosts.NaturalGas.FuelCostsId = fuelCosts.Id;
            }
            if (fuelCosts.Oil != null)
            {
                fuelCosts.Oil.FuelCostsId = fuelCosts.Id;
            }
            if (fuelCosts.Propane != null)
            {
                fuelCosts.Propane.FuelCostsId = fuelCosts.Id;
            }
            if (fuelCosts.Wood != null)
            {
                fuelCosts.Wood.FuelCostsId = fuelCosts.Id;
            }

            // Update the main FuelCosts entity (EF will handle the related entities automatically)
            _context.FuelCosts.Update(fuelCosts);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var fuelCosts = await _context.FuelCosts.FindAsync(id);
            if (fuelCosts != null)
            {
                _context.FuelCosts.Remove(fuelCosts);
                await _context.SaveChangesAsync();
            }
        }

        // Note: Using owned entities pattern - EF handles complex data automatically
        // No need for manual tracking or relationship management


    }
}
