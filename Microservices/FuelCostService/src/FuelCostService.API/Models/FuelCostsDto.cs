using System;
using System.Collections.Generic;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for main FuelCosts aggregate (matches FuelCosts.cs model)
    /// Enhanced to support both collection-based and ID-based fuel cost management
    /// </summary>
    public class FuelCostsDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public bool IncludeCostCalculations { get; set; }
        public string LibraryFile { get; set; } = string.Empty;

        // Individual fuel cost entities (following Type2 pattern from HvacService)
        // Each fuel type is now a separate entity with its own ID
        public ElectricityFuelCostDto? Electricity { get; set; }
        public NaturalGasFuelCostDto? NaturalGas { get; set; }
        public OilFuelCostDto? Oil { get; set; }
        public PropaneFuelCostDto? Propane { get; set; }
        public WoodFuelCostDto? Wood { get; set; }

        public FuelCostsMonthlyDto Monthly { get; set; } = new FuelCostsMonthlyDto();
    }
}
