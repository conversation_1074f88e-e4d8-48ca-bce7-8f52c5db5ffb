using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FuelCostService.Core.Interfaces;
using FuelCostService.Core.Models;

namespace FuelCostService.Core.Services
{
    /// <summary>
    /// Service implementation for FuelCosts business logic
    /// </summary>
    public class FuelCostService : IFuelCostService
    {
        private readonly IFuelCostRepository _repository;

        public FuelCostService(IFuelCostRepository repository)
        {
            _repository = repository;
        }

        public async Task<IEnumerable<FuelCosts>> GetAllFuelCostsAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<FuelCosts?> GetFuelCostsByHouseIdAsync(Guid houseId)
        {
            return await _repository.GetByHouseIdAsync(houseId);
        }

        public async Task<FuelCosts?> GetFuelCostsByIdAsync(Guid id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task<FuelCosts> CreateFuelCostsAsync(FuelCosts fuelCosts)
        {
            if (fuelCosts.Id == Guid.Empty)
                fuelCosts.Id = Guid.NewGuid();

            return await _repository.CreateAsync(fuelCosts);
        }

        public async Task UpdateFuelCostsAsync(FuelCosts fuelCosts)
        {
            await _repository.UpdateAsync(fuelCosts);
        }

        public async Task DeleteFuelCostsAsync(Guid id)
        {
            await _repository.DeleteAsync(id);
        }
    }
}
