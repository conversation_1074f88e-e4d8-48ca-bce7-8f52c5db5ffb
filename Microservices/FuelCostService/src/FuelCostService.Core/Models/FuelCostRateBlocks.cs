namespace FuelCostService.Core.Models
{
    /// <summary>
    /// Simple value object for fuel cost rate blocks container (used as owned entity)
    /// </summary>
    public class FuelCostRateBlocks
    {
        public FuelCostRateBlock Block1 { get; set; } = new FuelCostRateBlock();
        public FuelCostRateBlock Block2 { get; set; } = new FuelCostRateBlock();
        public FuelCostRateBlock Block3 { get; set; } = new FuelCostRateBlock();
        public FuelCostRateBlock Block4 { get; set; } = new FuelCostRateBlock();

        public FuelCostRateBlocks()
        {
        }

        public FuelCostRateBlocks(FuelCostRateBlocks toCopy)
        {
            if (toCopy != null)
            {
                Block1 = new FuelCostRateBlock(toCopy.Block1);
                Block2 = new FuelCostRateBlock(toCopy.Block2);
                Block3 = new FuelCostRateBlock(toCopy.Block3);
                Block4 = new FuelCostRateBlock(toCopy.Block4);
            }
        }
    }
}