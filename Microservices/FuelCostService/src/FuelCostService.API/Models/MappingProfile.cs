using System;
using AutoMapper;
using FuelCostService.Core.Models;
using FuelCostService.API.Models;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// AutoMapper profile for mapping between Core Models and DTOs
    /// </summary>
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Main FuelCosts mappings
            CreateMap<FuelCosts, FuelCostsDto>().ReverseMap();

            // Individual fuel cost entity mappings (following Type2 pattern)
            CreateMap<ElectricityFuelCost, ElectricityFuelCostDto>().ReverseMap();
            CreateMap<NaturalGasFuelCost, NaturalGasFuelCostDto>().ReverseMap();
            CreateMap<OilFuelCost, OilFuelCostDto>().ReverseMap();
            CreateMap<PropaneFuelCost, PropaneFuelCostDto>().ReverseMap();
            CreateMap<WoodFuelCost, WoodFuelCostDto>().ReverseMap();

            // FuelCostByType entity removed - now using individual fuel cost entities

            // CodeAndText mappings
            CreateMap<CodeAndText, CodeAndTextDto>().ReverseMap();

            // FuelCostMinimum mappings
            CreateMap<FuelCostMinimum, FuelCostMinimumDto>().ReverseMap();

            // FuelCostRateBlocks mappings
            CreateMap<FuelCostRateBlocks, FuelCostRateBlocksDto>().ReverseMap();

            // FuelCostRateBlock mappings
            CreateMap<FuelCostRateBlock, FuelCostRateBlockDto>().ReverseMap();

            // FuelCostsMonthly mappings
            CreateMap<FuelCostsMonthly, FuelCostsMonthlyDto>().ReverseMap();

            // FuelCostMonthlyData mappings
            CreateMap<FuelCostMonthlyData, FuelCostMonthlyDataDto>().ReverseMap();
        }
    }
}