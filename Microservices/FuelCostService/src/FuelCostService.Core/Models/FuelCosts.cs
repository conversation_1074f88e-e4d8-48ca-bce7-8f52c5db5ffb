using System;
using System.Collections.Generic;

namespace FuelCostService.Core.Models
{
    public class FuelCosts
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public bool IncludeCostCalculations { get; set; }
        public string LibraryFile { get; set; } = string.Empty;

        // Individual fuel cost entities (following Type2 pattern from HvacService)
        public ElectricityFuelCost? Electricity { get; set; }
        public NaturalGasFuelCost? NaturalGas { get; set; }
        public OilFuelCost? Oil { get; set; }
        public PropaneFuelCost? Propane { get; set; }
        public WoodFuelCost? Wood { get; set; }

        public FuelCostsMonthly Monthly { get; set; } = new FuelCostsMonthly();

        public FuelCosts()
        {
        }

        public FuelCosts(FuelCosts toCopy)
        {
            if (toCopy != null)
            {
                Id = Guid.NewGuid();
                HouseId = toCopy.HouseId;
                IncludeCostCalculations = toCopy.IncludeCostCalculations;
                LibraryFile = toCopy.LibraryFile;
                
                // Copy individual fuel cost entities (following Type2 pattern)
                Electricity = toCopy.Electricity != null ? new ElectricityFuelCost(toCopy.Electricity) : null;
                NaturalGas = toCopy.NaturalGas != null ? new NaturalGasFuelCost(toCopy.NaturalGas) : null;
                Oil = toCopy.Oil != null ? new OilFuelCost(toCopy.Oil) : null;
                Propane = toCopy.Propane != null ? new PropaneFuelCost(toCopy.Propane) : null;
                Wood = toCopy.Wood != null ? new WoodFuelCost(toCopy.Wood) : null;
                
                Monthly = new FuelCostsMonthly(toCopy.Monthly);
            }
        }

        // Note: Energy upgrade functionality removed - no longer needed
    }
}
