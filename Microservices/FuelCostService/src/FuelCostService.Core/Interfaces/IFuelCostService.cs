using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FuelCostService.Core.Models;

namespace FuelCostService.Core.Interfaces
{
    /// <summary>
    /// Service interface for FuelCosts business logic
    /// </summary>
    public interface IFuelCostService
    {
        /// <summary>
        /// Gets all fuel costs
        /// </summary>
        Task<IEnumerable<FuelCosts>> GetAllFuelCostsAsync();

        /// <summary>
        /// Gets fuel costs for a specific house
        /// </summary>
        Task<FuelCosts?> GetFuelCostsByHouseIdAsync(Guid houseId);

        /// <summary>
        /// Gets a specific fuel costs by ID
        /// </summary>
        Task<FuelCosts?> GetFuelCostsByIdAsync(Guid id);

        /// <summary>
        /// Creates new fuel costs
        /// </summary>
        Task<FuelCosts> CreateFuelCostsAsync(FuelCosts fuelCosts);

        /// <summary>
        /// Updates existing fuel costs
        /// </summary>
        Task UpdateFuelCostsAsync(FuelCosts fuelCosts);

        /// <summary>
        /// Deletes fuel costs by ID
        /// </summary>
        Task DeleteFuelCostsAsync(Guid id);
    }
}
