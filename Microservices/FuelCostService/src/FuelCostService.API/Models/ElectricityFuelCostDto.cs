using System;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for Electricity fuel cost entity (matches ElectricityFuelCost.cs model)
    /// </summary>
    public class ElectricityFuelCostDto
    {
        public Guid Id { get; set; }
        public Guid FuelCostsId { get; set; }
        public string Label { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        
        public CodeAndTextDto Units { get; set; } = new CodeAndTextDto();
        public FuelCostMinimumDto Minimum { get; set; } = new FuelCostMinimumDto();
        public FuelCostRateBlocksDto RateBlocks { get; set; } = new FuelCostRateBlocksDto();
    }
}
