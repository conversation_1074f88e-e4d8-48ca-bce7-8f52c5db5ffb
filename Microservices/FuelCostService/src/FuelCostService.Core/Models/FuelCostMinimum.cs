namespace FuelCostService.Core.Models
{
    /// <summary>
    /// Simple value object for fuel cost minimum charge (used as owned entity)
    /// </summary>
    public class FuelCostMinimum
    {
        public decimal Units { get; set; }
        public decimal Charge { get; set; }

        public FuelCostMinimum()
        {
        }

        public FuelCostMinimum(FuelCostMinimum toCopy)
        {
            if (toCopy != null)
            {
                Units = toCopy.Units;
                Charge = toCopy.Charge;
            }
        }
    }
}