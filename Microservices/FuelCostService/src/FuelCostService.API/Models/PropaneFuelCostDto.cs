using System;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for Propane fuel cost entity (matches PropaneFuelCost.cs model)
    /// </summary>
    public class PropaneFuelCostDto
    {
        public Guid Id { get; set; }
        public Guid FuelCostsId { get; set; }
        public string Label { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        
        public CodeAndTextDto Units { get; set; } = new CodeAndTextDto();
        public FuelCostMinimumDto Minimum { get; set; } = new FuelCostMinimumDto();
        public FuelCostRateBlocksDto RateBlocks { get; set; } = new FuelCostRateBlocksDto();
    }
}
