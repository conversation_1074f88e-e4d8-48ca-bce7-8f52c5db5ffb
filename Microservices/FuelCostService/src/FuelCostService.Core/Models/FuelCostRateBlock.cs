namespace FuelCostService.Core.Models
{
    /// <summary>
    /// Simple value object for individual fuel cost rate block (used as owned entity)
    /// </summary>
    public class FuelCostRateBlock
    {
        public decimal Units { get; set; }
        public decimal CostPerUnit { get; set; }

        public FuelCostRateBlock()
        {
        }

        public FuelCostRateBlock(FuelCostRateBlock toCopy)
        {
            if (toCopy != null)
            {
                Units = toCopy.Units;
                CostPerUnit = toCopy.CostPerUnit;
            }
        }
    }
}