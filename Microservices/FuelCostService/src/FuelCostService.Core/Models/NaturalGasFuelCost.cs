using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace FuelCostService.Core.Models
{
    /// <summary>
    /// Natural Gas fuel cost entity - follows Type2 pattern from HvacService
    /// </summary>
    public class NaturalGasFuelCost
    {
        public Guid Id { get; set; }
        
        public Guid FuelCostsId { get; set; }
        
        public string Label { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;

        // Navigation properties to existing entities
        public CodeAndText? Units { get; set; }
        public FuelCostMinimum? Minimum { get; set; }
        public FuelCostRateBlocks? RateBlocks { get; set; }

        // Navigation property back to parent
        [ForeignKey("FuelCostsId")]
        public FuelCosts FuelCosts { get; set; } = null!;

        public NaturalGasFuelCost()
        {
        }

        public NaturalGasFuelCost(NaturalGasFuelCost toCopy)
        {
            if (toCopy != null)
            {
                Id = Guid.NewGuid();
                Label = toCopy.Label;
                Comment = toCopy.Comment;
                Units = toCopy.Units != null ? new CodeAndText(toCopy.Units) : null;
                Minimum = toCopy.Minimum != null ? new FuelCostMinimum(toCopy.Minimum) : null;
                RateBlocks = toCopy.RateBlocks != null ? new FuelCostRateBlocks(toCopy.RateBlocks) : null;
            }
        }
    }
}
