using Microsoft.EntityFrameworkCore;
using FuelCostService.Core.Models;

namespace FuelCostService.Infrastructure.Data
{
    /// <summary>
    /// Entity Framework DbContext for fuel cost data
    /// </summary>
    public class FuelCostDbContext : DbContext
    {
        public FuelCostDbContext(DbContextOptions<FuelCostDbContext> options) : base(options)
        {
        }

        public DbSet<FuelCosts> FuelCosts { get; set; } = null!;

        // Individual fuel cost entities (following Type2 pattern)
        public DbSet<ElectricityFuelCost> ElectricityFuelCosts { get; set; } = null!;
        public DbSet<NaturalGasFuelCost> NaturalGasFuelCosts { get; set; } = null!;
        public DbSet<OilFuelCost> OilFuelCosts { get; set; } = null!;
        public DbSet<PropaneFuelCost> PropaneFuelCosts { get; set; } = null!;
        public DbSet<WoodFuelCost> WoodFuelCosts { get; set; } = null!;

        // Monthly data entities (kept for monthly fuel cost tracking)
        public DbSet<FuelCostsMonthly> FuelCostsMonthly { get; set; } = null!;
        public DbSet<FuelCostMonthlyData> FuelCostMonthlyData { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Set default schema
            modelBuilder.HasDefaultSchema("fuelcost");

            // Configure FuelCosts entity (main aggregate root)
            modelBuilder.Entity<FuelCosts>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HouseId).IsRequired();

                entity.Property(e => e.IncludeCostCalculations).IsRequired();
                entity.Property(e => e.LibraryFile).HasMaxLength(500);

                // Configure one-to-one relationships with individual fuel cost entities (following Type2 pattern)
                entity.HasOne(e => e.Electricity)
                      .WithOne(e => e.FuelCosts)
                      .HasForeignKey<ElectricityFuelCost>(e => e.FuelCostsId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.NaturalGas)
                      .WithOne(e => e.FuelCosts)
                      .HasForeignKey<NaturalGasFuelCost>(e => e.FuelCostsId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Oil)
                      .WithOne(e => e.FuelCosts)
                      .HasForeignKey<OilFuelCost>(e => e.FuelCostsId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Propane)
                      .WithOne(e => e.FuelCosts)
                      .HasForeignKey<PropaneFuelCost>(e => e.FuelCostsId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Wood)
                      .WithOne(e => e.FuelCosts)
                      .HasForeignKey<WoodFuelCost>(e => e.FuelCostsId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure one-to-one relationship with Monthly
                entity.HasOne(e => e.Monthly)
                      .WithOne(m => m.FuelCosts)
                      .HasForeignKey<FuelCostsMonthly>(m => m.FuelCostsId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Indexes for performance
                entity.HasIndex(e => e.HouseId).IsUnique();
            });

            // Legacy entities removed - now using individual fuel cost entities with owned entities

            // Configure FuelCostsMonthly entity
            modelBuilder.Entity<FuelCostsMonthly>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();

                // FIXED: Configure relationships with monthly data using NO ACTION to avoid cascade conflicts
                // Since the properties don't exist on the entity, we'll use shadow properties
                entity.HasOne(e => e.Electricity)
                      .WithMany()
                      .HasForeignKey("ElectricityId")
                      .OnDelete(DeleteBehavior.NoAction); // Changed from Cascade to NoAction

                entity.HasOne(e => e.NaturalGas)
                      .WithMany()
                      .HasForeignKey("NaturalGasId")
                      .OnDelete(DeleteBehavior.NoAction); // Changed from Cascade to NoAction

                entity.HasOne(e => e.Oil)
                      .WithMany()
                      .HasForeignKey("OilId")
                      .OnDelete(DeleteBehavior.NoAction); // Changed from Cascade to NoAction

                entity.HasOne(e => e.Propane)
                      .WithMany()
                      .HasForeignKey("PropaneId")
                      .OnDelete(DeleteBehavior.NoAction); // Changed from Cascade to NoAction

                entity.HasOne(e => e.Wood)
                      .WithMany()
                      .HasForeignKey("WoodId")
                      .OnDelete(DeleteBehavior.NoAction); // Changed from Cascade to NoAction

                // Index for performance
                entity.HasIndex(e => e.FuelCostsId);
            });

            // Configure FuelCostMonthlyData entity
            modelBuilder.Entity<FuelCostMonthlyData>(entity =>
            {
                entity.HasKey(e => e.Id);
                // Monthly values are stored as strings like in the original
                entity.Property(e => e.January).HasMaxLength(50);
                entity.Property(e => e.February).HasMaxLength(50);
                entity.Property(e => e.March).HasMaxLength(50);
                entity.Property(e => e.April).HasMaxLength(50);
                entity.Property(e => e.May).HasMaxLength(50);
                entity.Property(e => e.June).HasMaxLength(50);
                entity.Property(e => e.July).HasMaxLength(50);
                entity.Property(e => e.August).HasMaxLength(50);
                entity.Property(e => e.September).HasMaxLength(50);
                entity.Property(e => e.October).HasMaxLength(50);
                entity.Property(e => e.November).HasMaxLength(50);
                entity.Property(e => e.December).HasMaxLength(50);
            });

            // CodeAndText is now configured as owned entities in individual fuel cost entities

            // ===============================
            // INDIVIDUAL FUEL COST ENTITIES (following Type2 pattern)
            // ===============================

            // Configure ElectricityFuelCost entity
            modelBuilder.Entity<ElectricityFuelCost>(entity =>
            {
                entity.ToTable("ElectricityFuelCosts", "fuelcost");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();
                entity.Property(e => e.Label).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Comment).HasMaxLength(1000);

                // Configure owned entities - EF will embed these as columns in the same table
                entity.OwnsOne(e => e.Units, units =>
                {
                    units.Property(u => u.Code).HasColumnName("UnitsCode").HasMaxLength(50);
                    units.Property(u => u.Text).HasColumnName("UnitsText").HasMaxLength(200);
                });

                entity.OwnsOne(e => e.Minimum, minimum =>
                {
                    minimum.Property(m => m.Units).HasColumnName("MinimumUnits").HasPrecision(18, 6);
                    minimum.Property(m => m.Charge).HasColumnName("MinimumCharge").HasPrecision(18, 6);
                });

                entity.OwnsOne(e => e.RateBlocks, rateBlocks =>
                {
                    rateBlocks.OwnsOne(rb => rb.Block1, block1 =>
                    {
                        block1.Property(b => b.Units).HasColumnName("Block1Units").HasPrecision(18, 6);
                        block1.Property(b => b.CostPerUnit).HasColumnName("Block1CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block2, block2 =>
                    {
                        block2.Property(b => b.Units).HasColumnName("Block2Units").HasPrecision(18, 6);
                        block2.Property(b => b.CostPerUnit).HasColumnName("Block2CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block3, block3 =>
                    {
                        block3.Property(b => b.Units).HasColumnName("Block3Units").HasPrecision(18, 6);
                        block3.Property(b => b.CostPerUnit).HasColumnName("Block3CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block4, block4 =>
                    {
                        block4.Property(b => b.Units).HasColumnName("Block4Units").HasPrecision(18, 6);
                        block4.Property(b => b.CostPerUnit).HasColumnName("Block4CostPerUnit").HasPrecision(18, 6);
                    });
                });
            });

            // Configure other fuel cost entities with same pattern as Electricity
            // (Using owned entities for complex properties to avoid separate tables)
            ConfigureNaturalGasFuelCost(modelBuilder);
            ConfigureOilFuelCost(modelBuilder);
            ConfigurePropaneFuelCost(modelBuilder);
            ConfigureWoodFuelCost(modelBuilder);
        }

        /// <summary>
        /// Configure NaturalGasFuelCost entity with owned entities
        /// </summary>
        private void ConfigureNaturalGasFuelCost(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<NaturalGasFuelCost>(entity =>
            {
                entity.ToTable("NaturalGasFuelCosts", "fuelcost");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();
                entity.Property(e => e.Label).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Comment).HasMaxLength(1000);

                // Configure owned entities - EF will embed these as columns in the same table
                entity.OwnsOne(e => e.Units, units =>
                {
                    units.Property(u => u.Code).HasColumnName("UnitsCode").HasMaxLength(50);
                    units.Property(u => u.Text).HasColumnName("UnitsText").HasMaxLength(200);
                });

                entity.OwnsOne(e => e.Minimum, minimum =>
                {
                    minimum.Property(m => m.Units).HasColumnName("MinimumUnits").HasPrecision(18, 6);
                    minimum.Property(m => m.Charge).HasColumnName("MinimumCharge").HasPrecision(18, 6);
                });

                entity.OwnsOne(e => e.RateBlocks, rateBlocks =>
                {
                    rateBlocks.OwnsOne(rb => rb.Block1, block1 =>
                    {
                        block1.Property(b => b.Units).HasColumnName("Block1Units").HasPrecision(18, 6);
                        block1.Property(b => b.CostPerUnit).HasColumnName("Block1CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block2, block2 =>
                    {
                        block2.Property(b => b.Units).HasColumnName("Block2Units").HasPrecision(18, 6);
                        block2.Property(b => b.CostPerUnit).HasColumnName("Block2CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block3, block3 =>
                    {
                        block3.Property(b => b.Units).HasColumnName("Block3Units").HasPrecision(18, 6);
                        block3.Property(b => b.CostPerUnit).HasColumnName("Block3CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block4, block4 =>
                    {
                        block4.Property(b => b.Units).HasColumnName("Block4Units").HasPrecision(18, 6);
                        block4.Property(b => b.CostPerUnit).HasColumnName("Block4CostPerUnit").HasPrecision(18, 6);
                    });
                });
            });
        }

        /// <summary>
        /// Configure OilFuelCost entity with owned entities
        /// </summary>
        private void ConfigureOilFuelCost(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<OilFuelCost>(entity =>
            {
                entity.ToTable("OilFuelCosts", "fuelcost");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();
                entity.Property(e => e.Label).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Comment).HasMaxLength(1000);

                // Configure owned entities
                entity.OwnsOne(e => e.Units, units =>
                {
                    units.Property(u => u.Code).HasColumnName("UnitsCode").HasMaxLength(50);
                    units.Property(u => u.Text).HasColumnName("UnitsText").HasMaxLength(200);
                });

                entity.OwnsOne(e => e.Minimum, minimum =>
                {
                    minimum.Property(m => m.Units).HasColumnName("MinimumUnits").HasPrecision(18, 6);
                    minimum.Property(m => m.Charge).HasColumnName("MinimumCharge").HasPrecision(18, 6);
                });

                entity.OwnsOne(e => e.RateBlocks, rateBlocks =>
                {
                    rateBlocks.OwnsOne(rb => rb.Block1, block1 =>
                    {
                        block1.Property(b => b.Units).HasColumnName("Block1Units").HasPrecision(18, 6);
                        block1.Property(b => b.CostPerUnit).HasColumnName("Block1CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block2, block2 =>
                    {
                        block2.Property(b => b.Units).HasColumnName("Block2Units").HasPrecision(18, 6);
                        block2.Property(b => b.CostPerUnit).HasColumnName("Block2CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block3, block3 =>
                    {
                        block3.Property(b => b.Units).HasColumnName("Block3Units").HasPrecision(18, 6);
                        block3.Property(b => b.CostPerUnit).HasColumnName("Block3CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block4, block4 =>
                    {
                        block4.Property(b => b.Units).HasColumnName("Block4Units").HasPrecision(18, 6);
                        block4.Property(b => b.CostPerUnit).HasColumnName("Block4CostPerUnit").HasPrecision(18, 6);
                    });
                });
            });
        }

        /// <summary>
        /// Configure PropaneFuelCost entity with owned entities
        /// </summary>
        private void ConfigurePropaneFuelCost(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<PropaneFuelCost>(entity =>
            {
                entity.ToTable("PropaneFuelCosts", "fuelcost");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();
                entity.Property(e => e.Label).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Comment).HasMaxLength(1000);

                // Configure owned entities
                entity.OwnsOne(e => e.Units, units =>
                {
                    units.Property(u => u.Code).HasColumnName("UnitsCode").HasMaxLength(50);
                    units.Property(u => u.Text).HasColumnName("UnitsText").HasMaxLength(200);
                });

                entity.OwnsOne(e => e.Minimum, minimum =>
                {
                    minimum.Property(m => m.Units).HasColumnName("MinimumUnits").HasPrecision(18, 6);
                    minimum.Property(m => m.Charge).HasColumnName("MinimumCharge").HasPrecision(18, 6);
                });

                entity.OwnsOne(e => e.RateBlocks, rateBlocks =>
                {
                    rateBlocks.OwnsOne(rb => rb.Block1, block1 =>
                    {
                        block1.Property(b => b.Units).HasColumnName("Block1Units").HasPrecision(18, 6);
                        block1.Property(b => b.CostPerUnit).HasColumnName("Block1CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block2, block2 =>
                    {
                        block2.Property(b => b.Units).HasColumnName("Block2Units").HasPrecision(18, 6);
                        block2.Property(b => b.CostPerUnit).HasColumnName("Block2CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block3, block3 =>
                    {
                        block3.Property(b => b.Units).HasColumnName("Block3Units").HasPrecision(18, 6);
                        block3.Property(b => b.CostPerUnit).HasColumnName("Block3CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block4, block4 =>
                    {
                        block4.Property(b => b.Units).HasColumnName("Block4Units").HasPrecision(18, 6);
                        block4.Property(b => b.CostPerUnit).HasColumnName("Block4CostPerUnit").HasPrecision(18, 6);
                    });
                });
            });
        }

        /// <summary>
        /// Configure WoodFuelCost entity with owned entities
        /// </summary>
        private void ConfigureWoodFuelCost(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<WoodFuelCost>(entity =>
            {
                entity.ToTable("WoodFuelCosts", "fuelcost");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();
                entity.Property(e => e.Label).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Comment).HasMaxLength(1000);

                // Configure owned entities
                entity.OwnsOne(e => e.Units, units =>
                {
                    units.Property(u => u.Code).HasColumnName("UnitsCode").HasMaxLength(50);
                    units.Property(u => u.Text).HasColumnName("UnitsText").HasMaxLength(200);
                });

                entity.OwnsOne(e => e.Minimum, minimum =>
                {
                    minimum.Property(m => m.Units).HasColumnName("MinimumUnits").HasPrecision(18, 6);
                    minimum.Property(m => m.Charge).HasColumnName("MinimumCharge").HasPrecision(18, 6);
                });

                entity.OwnsOne(e => e.RateBlocks, rateBlocks =>
                {
                    rateBlocks.OwnsOne(rb => rb.Block1, block1 =>
                    {
                        block1.Property(b => b.Units).HasColumnName("Block1Units").HasPrecision(18, 6);
                        block1.Property(b => b.CostPerUnit).HasColumnName("Block1CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block2, block2 =>
                    {
                        block2.Property(b => b.Units).HasColumnName("Block2Units").HasPrecision(18, 6);
                        block2.Property(b => b.CostPerUnit).HasColumnName("Block2CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block3, block3 =>
                    {
                        block3.Property(b => b.Units).HasColumnName("Block3Units").HasPrecision(18, 6);
                        block3.Property(b => b.CostPerUnit).HasColumnName("Block3CostPerUnit").HasPrecision(18, 6);
                    });
                    rateBlocks.OwnsOne(rb => rb.Block4, block4 =>
                    {
                        block4.Property(b => b.Units).HasColumnName("Block4Units").HasPrecision(18, 6);
                        block4.Property(b => b.CostPerUnit).HasColumnName("Block4CostPerUnit").HasPrecision(18, 6);
                    });
                });
            });
        }
    }
}